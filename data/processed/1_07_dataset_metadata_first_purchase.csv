key,value
dataset_name,cleaned_customer_data_first_purchase
strategy,First Purchase Segmentation
shape,"(96470, 9)"
columns,"['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment']"
dtypes,"{'customer_id': 'object', 'frequency': 'int64', 'first_order_date': 'datetime64[ns]', 'last_order_date': 'datetime64[ns]', 'recency': 'int64', 'customer_state': 'object', 'customer_city': 'object', 'monetary': 'float64', 'value_segment': 'category'}"
data_quality,"{'mono_buyers_percentage': np.float64(100.0), 'avg_recency_days': np.float64(239.13), 'avg_monetary_value': np.float64(137.04), 'missing_values': {'customer_id': 0, 'frequency': 0, 'first_order_date': 0, 'last_order_date': 0, 'recency': 0, 'customer_state': 0, 'customer_city': 0, 'monetary': 0, 'value_segment': 0}}"
processing_steps,"['Chargement depuis SQLite', '🚨 DÉCOUVERTE : Pattern 1 client = 1 commande', 'Filtrage commandes livrées uniquement', 'Création métriques RFM adaptées', 'Analyse outliers contextualisée', 'Visualisations spécialisées First Purchase', 'Préparation pour segmentation adaptée']"
next_steps,"['Feature Engineering adapté (Notebook 2)', 'Clustering First Purchase (Notebook 3)', 'Stratégies acquisition/réactivation (Notebook 4)']"
