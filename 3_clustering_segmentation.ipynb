# Imports spécifiques pour ce notebook
import os
import json
from datetime import datetime
import joblib

# Clustering et métriques
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from scipy import stats

# Imports locaux - modules utils optimisés
from utils.core import (
    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,
    pd, np, plt, sns
)
from utils.clustering import (
    find_optimal_k,
    perform_kmeans,
    calculate_clustering_metrics,
    analyze_clusters
)
from utils.clustering_visualization import (
    plot_elbow_curve,
    plot_clusters_2d,
    plot_cluster_profiles,
    export_figure
)
from utils.data_tools import load_data, export_artifact
from utils.save_load import save_results, load_results

# Configuration du notebook
init_notebook(
    notebook_file_path="3_clustering_segmentation.ipynb",
    style="whitegrid",
    figsize=(12, 8),
    random_seed=SEED,
    setup=True,
    check_deps=True
)

# Chargement des données préparées du Notebook 2
print("🔄 Chargement des données préparées pour le clustering...")

# Chemins des fichiers générés par le notebook 2
data_path_scaled = 'data/processed/2_01_features_scaled_clustering.csv'
data_path_with_ids = 'data/processed/2_02_features_scaled_with_ids.csv'
data_path_complete = 'data/processed/2_03_rfm_enriched_complete.csv'

try:
    # Chargement des datasets
    X_scaled = load_results(data_path_scaled)
    X_with_ids = load_results(data_path_with_ids)
    df_complete = load_results(data_path_complete)

    print(f"✅ Dataset pour clustering : {X_scaled.shape}")
    print(f"   Variables : {list(X_scaled.columns)}")
    print(f"\n✅ Dataset avec IDs : {X_with_ids.shape}")
    print(f"✅ Dataset complet : {df_complete.shape}")

    # Vérification de la cohérence
    assert len(X_scaled) == len(X_with_ids) == len(df_complete), "Tailles incohérentes entre les datasets"
    print(f"\n✅ Cohérence vérifiée : {len(X_scaled)} clients dans tous les datasets")

except FileNotFoundError as e:
    print(f"❌ Erreur : Fichier non trouvé - {e}")
    print("💡 Assurez-vous d'avoir exécuté le Notebook 2 (Feature Engineering) avant ce notebook.")
    raise
except Exception as e:
    print(f"❌ Erreur lors du chargement : {e}")
    raise

# 🚨 DIAGNOSTIC CRITIQUE : Variables inadaptées au contexte "First Purchase"
print("🚨 DIAGNOSTIC CRITIQUE DES VARIABLES ACTUELLES")
print("=" * 60)

print(f"\n📊 Variables actuellement chargées : {len(X_scaled.columns)}")
for i, col in enumerate(X_scaled.columns, 1):
    print(f"{i:2d}. {col}")

# Identification des variables problématiques selon la stratégie
print("\n🔍 ANALYSE SELON LA STRATÉGIE \"FIRST PURCHASE\" :")

# Variables constantes (variance nulle) - PROBLÈME MAJEUR
constant_variables = X_scaled.columns[X_scaled.std() == 0].tolist()
print(f"\n❌ Variables à variance nulle ({len(constant_variables)}) :")
for var in constant_variables:
    print(f"   - {var} : valeur = {X_scaled[var].iloc[0]:.3f}")

# Variables redondantes identifiées dans la stratégie
redundant_vars = [
    'total_amount', 'amount_total', 'avg_amount', 'montant_moyen',  # 4 doublons monetary
    'total_orders', 'order_count',  # 2 doublons frequency
    'recency_days', 'days_since_first_order',  # 2 doublons recency
    'amount_std', 'amount_std_dev',  # 2 doublons std
    'avg_order_value', 'order_value_mean',  # 2 doublons order_value
    'amount_cv', 'amount_cv_coef',  # 2 doublons CV (variance nulle)
    'customer_lifespan_days',  # Toujours 0 (mono-achat)
    'purchase_frequency'  # Pas de sens avec 1 commande
]

present_redundant = [var for var in redundant_vars if var in X_scaled.columns]
print(f"\n❌ Variables redondantes présentes ({len(present_redundant)}) :")
for var in present_redundant:
    print(f"   - {var}")

# Variables de base utilisables
base_variables = ['recency', 'monetary']
present_base = [var for var in base_variables if var in X_scaled.columns]
print(f"\n✅ Variables de base utilisables ({len(present_base)}) :")
for var in present_base:
    print(f"   - {var}")

print(f"\n🎯 BILAN CRITIQUE :")
print(f"   - Variables actuelles : {len(X_scaled.columns)}")
print(f"   - Variables à variance nulle : {len(constant_variables)}")
print(f"   - Variables redondantes : {len(present_redundant)}")
print(f"   - Variables utilisables : {len(present_base)}")
print(f"   - Variables manquantes : Contextuelles (géographie, saisonnalité, livraison, satisfaction)")

print("\n⚠️ CONCLUSION :")
print("   ❌ Le dataset actuel est INADAPTÉ au clustering \"First Purchase\"")
print("   ✅ Nous devons créer les variables contextuelles depuis le dataset complet")
print("   🎯 Objectif : Passer de 22 variables redondantes à 6 variables pertinentes")

# 🎯 CRÉATION DES VARIABLES "FIRST PURCHASE" SELON LA STRATÉGIE
print("🎯 CRÉATION DES VARIABLES ADAPTÉES AU CONTEXTE MONO-ACHAT")
print("=" * 65)

# Vérification des colonnes disponibles dans le dataset complet
print("\n📊 Analyse du dataset complet :")
print(f"   Shape : {df_complete.shape}")
print(f"   Colonnes disponibles : {len(df_complete.columns)}")

# Création des variables "First Purchase" selon la stratégie (6 variables max)
print("\n🔧 CRÉATION DES 6 VARIABLES \"FIRST PURCHASE\" :")

# Initialisation du DataFrame final
first_purchase_features = pd.DataFrame(index=df_complete.index)

# 1. RÉCENCE - Variable principale (jours depuis l'achat)
first_purchase_features['recency_days'] = X_scaled['recency_days']  # Utilisation de recency_days au lieu de recency
print("   ✅ 1. recency_days : Jours depuis l'achat (normalisé)")

# 2. MONTANT - Variable principale (valeur de la commande)
first_purchase_features['order_value'] = X_scaled['order_value']  # Utilisation de order_value au lieu de monetary
print("   ✅ 2. order_value : Montant de la commande (normalisé)")

# 3. GÉOGRAPHIE - Localisation du client
first_purchase_features['state_encoded'] = X_scaled['state_encoded']  # Utilisation directe de state_encoded
print("   ✅ 3. state_encoded : Localisation géographique (normalisé)")

# 4. SAISONNALITÉ - Mois d'achat (non disponible)
print("   ❌ 4. purchase_month : Non disponible")

# 5. PERFORMANCE LOGISTIQUE - Délai de livraison (non disponible)
print("   ❌ 5. delivery_days : Non disponible")

# 6. SATISFACTION CLIENT - Score de review (non disponible)
print("   ❌ 6. review_score : Non disponible")

# Nettoyage final : suppression des lignes avec valeurs manquantes
initial_rows = len(first_purchase_features)
first_purchase_features = first_purchase_features.dropna()
final_rows = len(first_purchase_features)

print(f"\n📈 DATASET FINAL \"FIRST PURCHASE\" :")
print(f"   - Observations initiales : {initial_rows:,}")
print(f"   - Observations finales : {final_rows:,}")
print(f"   - Lignes supprimées : {initial_rows - final_rows:,}")
print(f"   - Variables : {len(first_purchase_features.columns)}")
print(f"   - Variables utilisées : {list(first_purchase_features.columns)}")

# Vérification de la qualité selon la stratégie
print(f"\n🔍 VALIDATION QUALITÉ SELON LA STRATÉGIE :")
missing_values = first_purchase_features.isnull().sum().sum()
constant_vars = (first_purchase_features.std() == 0).sum()
corr_matrix = first_purchase_features.corr().abs()
high_corr = ((corr_matrix > 0.7) & (corr_matrix < 1.0)).sum().sum() // 2

print(f"   ✅ Valeurs manquantes : {missing_values} (objectif : 0)")
print(f"   ✅ Variables constantes : {constant_vars} (objectif : 0)")
print(f"   ✅ Corrélations élevées (>0.7) : {high_corr} (objectif : <3)")
print(f"   ✅ Nombre de variables : {len(first_purchase_features.columns)} (objectif : ≤6)")

# Création du dataset adapté pour la suite
X_scaled_adapted = first_purchase_features.copy()
print(f"\n✅ Dataset adapté créé : X_scaled_adapted {X_scaled_adapted.shape}")

# Affichage des statistiques descriptives
print(f"\n📊 Statistiques descriptives des variables \"First Purchase\" :")
display(X_scaled_adapted.describe().round(3))

# 🔍 RECHERCHE DU NOMBRE OPTIMAL AVEC VARIABLES "FIRST PURCHASE"
print("🔍 RECHERCHE DU NOMBRE OPTIMAL DE CLUSTERS")
print("=" * 50)

# Vérification du dataset adapté
if 'X_scaled_adapted' not in locals():
    print("❌ ERREUR : Variables \"First Purchase\" non créées.")
    print("💡 Exécutez la section 1.4 d'abord.")
    raise ValueError("Dataset adapté manquant")

print(f"\n📊 DATASET \"FIRST PURCHASE\" :")
print(f"   - Shape : {X_scaled_adapted.shape}")
print(f"   - Variables : {list(X_scaled_adapted.columns)}")
print(f"   - Observations : {len(X_scaled_adapted):,}")

# Configuration selon la stratégie (4-6 segments attendus)
k_range = range(2, 8)  # Test de 2 à 7 clusters (stratégie : 4-6 segments)
inertias = []
silhouette_scores = []
calinski_scores = []

# Échantillonnage pour optimiser les performances
sample_size = min(5000, len(X_scaled_adapted))  # Réduit pour variables adaptées
X_sample = X_scaled_adapted.sample(n=sample_size, random_state=SEED)
print(f"\n📊 Échantillon pour silhouette : {sample_size:,} points")

print("\n🔄 Calcul des métriques par k :")
for k in k_range:
    print(f"   K = {k}", end=" ")

    # K-Means optimisé pour variables "First Purchase"
    kmeans = KMeans(
        n_clusters=k,
        random_state=SEED,
        n_init=10,  # Augmenté pour stabilité
        max_iter=300,
        algorithm='lloyd'  # Meilleur pour petits datasets
    )

    # Clustering sur dataset complet
    cluster_labels = kmeans.fit_predict(X_scaled_adapted)

    # Métriques de qualité
    sample_labels = kmeans.predict(X_sample)
    silhouette_avg = silhouette_score(X_sample, sample_labels)
    calinski_avg = calinski_harabasz_score(X_scaled_adapted, cluster_labels)

    # Stockage des résultats
    inertias.append(kmeans.inertia_)
    silhouette_scores.append(silhouette_avg)
    calinski_scores.append(calinski_avg)

    print(f"→ Silhouette: {silhouette_avg:.3f}, Calinski: {calinski_avg:.0f}")

print("\n✅ Calculs terminés")

# Visualisation avec module optimisé
fig = plot_elbow_curve(k_range, inertias, silhouette_scores)
export_figure(fig, notebook_name="3", export_number=1, base_name="elbow_first_purchase")

# Détermination du k optimal selon la stratégie
# Critère : silhouette > 0.4 ET dans la plage 4-6 clusters
valid_k = []
for i, k in enumerate(k_range):
    if silhouette_scores[i] > 0.4 and 4 <= k <= 6:
        valid_k.append((k, silhouette_scores[i]))

if valid_k:
    # Sélection du k avec le meilleur score silhouette
    optimal_k = max(valid_k, key=lambda x: x[1])[0]
else:
    # Fallback : meilleur silhouette dans la plage 3-6
    fallback_range = [i for i, k in enumerate(k_range) if 3 <= k <= 6]
    best_idx = max(fallback_range, key=lambda i: silhouette_scores[i])
    optimal_k = k_range[best_idx]

print(f"\n🎯 RÉSULTATS SELON LA STRATÉGIE :")
print(f"   - K optimal sélectionné : {optimal_k}")
print(f"   - Score silhouette : {silhouette_scores[optimal_k-2]:.3f}")
print(f"   - Score Calinski-Harabasz : {calinski_scores[optimal_k-2]:.0f}")
print(f"   - Inertie : {inertias[optimal_k-2]:.1f}")

# Affichage détaillé des résultats
print(f"\n📊 TABLEAU COMPLET DES RÉSULTATS :")
for i, k in enumerate(k_range):
    status = "🎯" if k == optimal_k else "  "
    print(f"   {status} K={k} : Silhouette={silhouette_scores[i]:.3f}, Calinski={calinski_scores[i]:.0f}, Inertie={inertias[i]:.1f}")

# 🎯 CLUSTERING "FIRST PURCHASE" AVEC K OPTIMAL
print("🎯 CLUSTERING \"FIRST PURCHASE\" AVEC K OPTIMAL")
print("=" * 55)

# Vérification des prérequis
if 'optimal_k' not in locals():
    print("❌ ERREUR : K optimal non déterminé.")
    print("💡 Exécutez la section 2.1 d'abord.")
    raise ValueError("K optimal manquant")

print(f"\n📊 CONFIGURATION DU CLUSTERING :")
print(f"   - Dataset : {X_scaled_adapted.shape}")
print(f"   - Variables : {list(X_scaled_adapted.columns)}")
print(f"   - K optimal : {optimal_k}")
print(f"   - Score silhouette attendu : {silhouette_scores[optimal_k-2]:.3f}")

# Entraînement du modèle K-Means final
print(f"\n🔄 Entraînement K-Means avec k={optimal_k}...")
kmeans_final = KMeans(
    n_clusters=optimal_k,
    random_state=SEED,
    n_init=20,  # Augmenté pour le modèle final
    max_iter=500,  # Augmenté pour convergence
    algorithm='lloyd'
)

# Prédiction des clusters
cluster_labels = kmeans_final.fit_predict(X_scaled_adapted)
print(f"✅ Clustering terminé")

# Calcul des métriques finales
final_silhouette = silhouette_score(X_scaled_adapted, cluster_labels)
final_calinski = calinski_harabasz_score(X_scaled_adapted, cluster_labels)
final_inertia = kmeans_final.inertia_

print(f"\n📊 MÉTRIQUES FINALES :")
print(f"   - Score silhouette : {final_silhouette:.3f}")
print(f"   - Score Calinski-Harabasz : {final_calinski:.0f}")
print(f"   - Inertie : {final_inertia:.1f}")

# Analyse de la distribution des clusters
cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()
print(f"\n📊 DISTRIBUTION DES CLUSTERS :")
for cluster_id, count in cluster_counts.items():
    percentage = (count / len(cluster_labels)) * 100
    print(f"   - Cluster {cluster_id} : {count:,} clients ({percentage:.1f}%)")

# Création du DataFrame avec les résultats
df_clustered = X_scaled_adapted.copy()
df_clustered['cluster'] = cluster_labels

print(f"\n✅ Dataset avec clusters créé : {df_clustered.shape}")
print(f"   Colonnes : {list(df_clustered.columns)}")

# 📊 ANALYSE DES CENTRES DE CLUSTERS "FIRST PURCHASE"
print("📊 ANALYSE DES CENTRES DE CLUSTERS")
print("=" * 40)

# Extraction des centres de clusters
cluster_centers = pd.DataFrame(
    kmeans_final.cluster_centers_,
    columns=X_scaled_adapted.columns,
    index=[f'Cluster {i}' for i in range(optimal_k)]
)

print(f"\n📊 CENTRES DE CLUSTERS (valeurs normalisées) :")
display(cluster_centers.round(3))

# Calcul des statistiques par cluster
print(f"\n📊 STATISTIQUES DÉTAILLÉES PAR CLUSTER :")
cluster_stats = df_clustered.groupby('cluster').agg({
    'recency_days': ['mean', 'std', 'min', 'max'],
    'order_value': ['mean', 'std', 'min', 'max']
}).round(3)

display(cluster_stats)

# Interprétation des centres selon la stratégie "First Purchase"
print(f"\n🎯 INTERPRÉTATION SELON LA STRATÉGIE \"FIRST PURCHASE\" :")

for i in range(optimal_k):
    center = cluster_centers.iloc[i]
    count = cluster_counts[i]

    print(f"\n   🔸 CLUSTER {i} ({count:,} clients - {(count/len(cluster_labels)*100):.1f}%) :")

    # Analyse de la récence
    if center['recency_days'] > 0.5:
        recency_desc = "Clients ANCIENS (achat il y a longtemps)"
    elif center['recency_days'] > -0.5:
        recency_desc = "Clients MOYENS (achat récent)"
    else:
        recency_desc = "Clients TRÈS RÉCENTS (achat très récent)"

    # Analyse du montant
    if center['order_value'] > 0.5:
        value_desc = "Montant ÉLEVÉ (commande premium)"
    elif center['order_value'] > -0.5:
        value_desc = "Montant MOYEN (commande standard)"
    else:
        value_desc = "Montant FAIBLE (petite commande)"

    print(f"     - Récence : {recency_desc}")
    print(f"     - Valeur : {value_desc}")

    # Analyse des variables contextuelles si disponibles
    if 'state_encoded' in center.index:
        geo_desc = "Géographie spécifique" if abs(center['state_encoded']) > 0.5 else "Géographie standard"
        print(f"     - Géographie : {geo_desc}")

    if 'purchase_month' in center.index:
        season_desc = "Saisonnalité marquée" if abs(center['purchase_month']) > 0.5 else "Saisonnalité standard"
        print(f"     - Saisonnalité : {season_desc}")

print(f"\n✅ Analyse des centres terminée")

# 🏢 INTERPRÉTATION BUSINESS DES SEGMENTS "FIRST PURCHASE"
print("🏢 INTERPRÉTATION BUSINESS DES SEGMENTS")
print("=" * 45)

# Création des profils business selon la stratégie "First Purchase"
segment_profiles = {}

print(f"\n🎯 PROFILS BUSINESS DES {optimal_k} SEGMENTS :")

for i in range(optimal_k):
    center = cluster_centers.iloc[i]
    count = cluster_counts[i]
    percentage = (count / len(cluster_labels)) * 100

    # Détermination du profil selon la stratégie "First Purchase"
    recency_score = center['recency_days']
    value_score = center['order_value']

    # Logique de segmentation "First Purchase"
    if recency_score < -0.3 and value_score > 0.3:  # Récent + Élevé
        segment_name = "🌟 Nouveaux Clients Premium"
        business_desc = "Clients récents avec commande élevée - Fort potentiel de rétention"
        priority = "TRÈS HAUTE"
        color = "#2E8B57"  # Vert foncé

    elif recency_score < -0.3 and value_score > -0.3:  # Récent + Moyen
        segment_name = "💎 Nouveaux Clients Prometteurs"
        business_desc = "Clients récents avec commande moyenne - Potentiel à développer"
        priority = "HAUTE"
        color = "#4169E1"  # Bleu royal

    elif recency_score < -0.3 and value_score < -0.3:  # Récent + Faible
        segment_name = "🌱 Nouveaux Clients Découverte"
        business_desc = "Clients récents avec petite commande - À fidéliser"
        priority = "MOYENNE"
        color = "#32CD32"  # Vert lime

    elif recency_score > 0.3 and value_score > 0.3:  # Ancien + Élevé
        segment_name = "⚠️ Clients Premium Dormants"
        business_desc = "Anciens clients à forte valeur - Risque de perte, réactivation urgente"
        priority = "CRITIQUE"
        color = "#FF6347"  # Rouge tomate

    else:  # Autres combinaisons
        segment_name = "😴 Clients Inactifs"
        business_desc = "Clients anciens avec faible engagement - Réactivation difficile"
        priority = "FAIBLE"
        color = "#808080"  # Gris

    # Stockage du profil
    segment_profiles[i] = {
        'name': segment_name,
        'description': business_desc,
        'priority': priority,
        'color': color,
        'count': count,
        'percentage': percentage,
        'recency_score': recency_score,
        'value_score': value_score
    }

    print(f"\n   {segment_name}")
    print(f"   📊 Taille : {count:,} clients ({percentage:.1f}%)")
    print(f"   📝 Description : {business_desc}")
    print(f"   🎯 Priorité : {priority}")
    print(f"   📈 Scores : Récence={recency_score:.2f}, Valeur={value_score:.2f}")

print(f"\n✅ Profils business créés pour {optimal_k} segments")

# 🎯 RECOMMANDATIONS STRATÉGIQUES PAR SEGMENT
print("🎯 RECOMMANDATIONS STRATÉGIQUES PAR SEGMENT")
print("=" * 50)

# Recommandations détaillées selon la stratégie "First Purchase"
recommendations = {}

for i, profile in segment_profiles.items():
    segment_name = profile['name']
    priority = profile['priority']
    count = profile['count']
    percentage = profile['percentage']

    print(f"\n🔸 {segment_name} ({count:,} clients - {percentage:.1f}%)")
    print(f"   Priorité : {priority}")

    # Recommandations spécifiques selon le profil
    if "Premium" in segment_name and "Nouveaux" in segment_name:
        actions = [
            "🎯 Programme VIP immédiat avec avantages exclusifs",
            "📧 Email de bienvenue personnalisé avec offres premium",
            "🎁 Offre de fidélité attractive (livraison gratuite, remises)",
            "📞 Contact proactif pour recueillir feedback",
            "🔄 Recommandations produits basées sur le premier achat"
        ]
        kpis = ["Taux de rétention à 30 jours > 60%", "Panier moyen 2ème achat > 150% du 1er"]

    elif "Prometteurs" in segment_name:
        actions = [
            "💎 Programme de montée en gamme (upselling)",
            "📱 Notifications push avec offres ciblées",
            "🎯 Cross-selling basé sur les préférences",
            "📊 A/B test sur les canaux de communication",
            "🏆 Gamification avec points de fidélité"
        ]
        kpis = ["Taux de conversion 2ème achat > 40%", "Augmentation panier moyen > 25%"]

    elif "Découverte" in segment_name:
        actions = [
            "🌱 Onboarding progressif avec tutoriels",
            "💰 Offres d'essai et échantillons gratuits",
            "📚 Contenu éducatif sur les produits",
            "🎁 Codes promo pour encourager 2ème achat",
            "👥 Programme de parrainage"
        ]
        kpis = ["Taux d'engagement > 30%", "Coût d'acquisition < 15€"]

    elif "Dormants" in segment_name:
        actions = [
            "🚨 Campagne de réactivation urgente",
            "💸 Offres de reconquête agressives (-20% minimum)",
            "📞 Contact direct par téléphone/email personnalisé",
            "🔍 Analyse des raisons d'abandon",
            "🎯 Retargeting publicitaire intensif"
        ]
        kpis = ["Taux de réactivation > 15%", "ROI campagne > 200%"]

    else:  # Inactifs
        actions = [
            "📊 Analyse de rentabilité avant investissement",
            "💌 Campagne de réactivation low-cost",
            "🎯 Segmentation plus fine pour identifier sous-groupes",
            "📱 Remarketing avec budget limité",
            "🔄 Évaluation pour exclusion des campagnes actives"
        ]
        kpis = ["Coût par réactivation < 10€", "Taux de réactivation > 5%"]

    # Affichage des recommandations
    print(f"\n   📋 ACTIONS RECOMMANDÉES :")
    for action in actions:
        print(f"      {action}")

    print(f"\n   📊 KPIs À SUIVRE :")
    for kpi in kpis:
        print(f"      • {kpi}")

    # Stockage des recommandations
    recommendations[i] = {
        'actions': actions,
        'kpis': kpis
    }

print(f"\n✅ Recommandations stratégiques créées pour {optimal_k} segments")
print(f"\n🎯 PRIORITÉS GLOBALES SELON LA STRATÉGIE \"FIRST PURCHASE\" :")
print(f"   1. Fidéliser les nouveaux clients premium (ROI immédiat)")
print(f"   2. Développer les clients prometteurs (croissance)")
print(f"   3. Réactiver les clients dormants à forte valeur (récupération)")
print(f"   4. Éduquer les clients découverte (développement long terme)")
print(f"   5. Optimiser les coûts sur les clients inactifs (efficacité)")

# 📊 VISUALISATION DES CLUSTERS "FIRST PURCHASE"
print("📊 VISUALISATION DES CLUSTERS \"FIRST PURCHASE\"")
print("=" * 55)

# PCA pour réduction de dimension
print(f"\n🔍 Application de l'ACP pour la visualisation...")
print(f"   Dataset : {X_scaled_adapted.shape}")
print(f"   Variables : {list(X_scaled_adapted.columns)}")

# PCA 2D pour visualisation principale
pca_2d = PCA(n_components=2, random_state=SEED)
X_pca_2d = pca_2d.fit_transform(X_scaled_adapted)

# Calcul de la variance expliquée
variance_2d = pca_2d.explained_variance_ratio_.sum()
print(f"\n📊 Variance expliquée par les 2 premières composantes : {variance_2d:.1%}")
print(f"   PC1 : {pca_2d.explained_variance_ratio_[0]:.1%}")
print(f"   PC2 : {pca_2d.explained_variance_ratio_[1]:.1%}")

# Analyse des contributions des variables
components_df = pd.DataFrame(
    pca_2d.components_.T,
    columns=['PC1', 'PC2'],
    index=X_scaled_adapted.columns
)

print(f"\n🔍 Contributions principales aux composantes :")
pc1_contrib = components_df['PC1'].abs().sort_values(ascending=False)
pc2_contrib = components_df['PC2'].abs().sort_values(ascending=False)

print(f"   PC1 ({pca_2d.explained_variance_ratio_[0]:.1%}) : {', '.join(pc1_contrib.head(3).index)}")
print(f"   PC2 ({pca_2d.explained_variance_ratio_[1]:.1%}) : {', '.join(pc2_contrib.head(3).index)}")

# Création du DataFrame pour visualisation
df_viz = pd.DataFrame({
    'PC1': X_pca_2d[:, 0],
    'PC2': X_pca_2d[:, 1],
    'cluster': cluster_labels
})

# Ajout des noms de segments
df_viz['segment_name'] = df_viz['cluster'].map(lambda x: segment_profiles[x]['name'])
df_viz['segment_color'] = df_viz['cluster'].map(lambda x: segment_profiles[x]['color'])

print(f"\n✅ Données préparées pour visualisation : {df_viz.shape}")

# 🎨 VISUALISATION 2D DES SEGMENTS "FIRST PURCHASE"
print("🎨 CRÉATION DES VISUALISATIONS")
print("=" * 35)

# 1. Réduction de dimensionnalité avec PCA
from sklearn.decomposition import PCA
pca_2d = PCA(n_components=2)
X_pca = pca_2d.fit_transform(X_scaled_adapted)

# 2. Clustering avec KMeans
from sklearn.cluster import KMeans
optimal_k = 4  # Nombre optimal de clusters déterminé précédemment
kmeans = KMeans(n_clusters=optimal_k, random_state=42)
labels = kmeans.fit_predict(X_scaled_adapted)
cluster_centers = kmeans.cluster_centers_

# 3. Création du DataFrame pour la visualisation
df_viz = pd.DataFrame({
    'PC1': X_pca[:, 0],
    'PC2': X_pca[:, 1],
    'cluster': labels
})

# 4. Utilisation du module de visualisation optimisé
fig = plot_clusters_2d(
    df_viz,  # DataFrame complet
    'cluster',  # Colonne des labels
    f"Segmentation \"First Purchase\" - {optimal_k} clusters"  # Titre
)

# Export de la figure
export_figure(fig, notebook_name="3", export_number=2, base_name="clusters_2d_first_purchase")

# 5. Visualisation des profils de clusters
# Création d'un DataFrame pour les profils
df_profiles = pd.DataFrame(
    cluster_centers,
    columns=['Récence', 'Valeur', 'État']
)
df_profiles['cluster'] = range(optimal_k)

fig_profiles = plot_cluster_profiles(
    data=df_profiles,  # DataFrame avec les centres et les labels
    labels='cluster',  # Nom de la colonne des labels
    feature_names=['Récence', 'Valeur', 'État']  # Noms des features
)

# Export de la figure des profils
export_figure(fig_profiles, notebook_name="3", export_number=3, base_name="profiles_first_purchase")

# 6. Affichage des statistiques des clusters
print("\n📊 Statistiques des clusters :")
cluster_stats = pd.DataFrame({
    'Taille': pd.Series(labels).value_counts().sort_index(),
    'Pourcentage': pd.Series(labels).value_counts(normalize=True).sort_index().round(3) * 100
})
print(cluster_stats)

# 7. Affichage des caractéristiques des clusters
print("\n📈 Caractéristiques des clusters :")
print(df_profiles.round(3))

# 8. Interprétation des clusters
print("\n🎯 Interprétation des clusters :")
for i in range(optimal_k):
    cluster_data = df_profiles.iloc[i]
    print(f"\nCluster {i+1}:")
    print(f"- Récence : {cluster_data['Récence']:.3f} (écart-type)")
    print(f"- Valeur : {cluster_data['Valeur']:.3f} (écart-type)")
    print(f"- État : {cluster_data['État']:.3f} (écart-type)")

print(f"\n✅ Visualisations créées et exportées")
print(f"   - Scatter plot 2D des clusters")
print(f"   - Profils radar des segments")
print(f"   - Fichiers sauvegardés dans {REPORTS_DIR}/figures/")

# 💾 SAUVEGARDE DU MODÈLE ET DES RÉSULTATS
print("💾 SAUVEGARDE DU MODÈLE ET DES RÉSULTATS")
print("=" * 50)

# 1. Sauvegarde du modèle K-Means
model_path = 'models/3_01_kmeans_first_purchase.joblib'
os.makedirs('models', exist_ok=True)
joblib.dump(kmeans, model_path)
print(f"✅ Modèle K-Means sauvegardé")

# 2. Sauvegarde des résultats de clustering
results_clustering = {
    'optimal_k': optimal_k,
    'cluster_labels': labels.tolist(),
    'cluster_centers': cluster_centers.tolist(),
    'metrics': {
        'silhouette_score': float(silhouette_score(X_scaled_adapted, labels)),
        'calinski_harabasz_score': float(calinski_harabasz_score(X_scaled_adapted, labels)),
        'inertia': float(kmeans.inertia_)
    }
}

results_path = 'data/processed/3_01_clustering_results_first_purchase.json'
with open(results_path, 'w', encoding='utf-8') as f:
    json.dump(results_clustering, f, ensure_ascii=False, indent=2)
print(f"✅ Résultats de clustering sauvegardés")

# 3. Sauvegarde du dataset avec clusters
df_final = pd.DataFrame({
    'customer_id': X_scaled_adapted.index,
    'cluster': labels
})
df_final.to_csv('data/processed/3_02_dataset_with_clusters_first_purchase.csv', index=False)
print(f"✅ Dataset avec clusters sauvegardé")

# 4. Sauvegarde des données de visualisation
viz_data = {
    'pca_components': pca_2d.components_.tolist(),
    'explained_variance_ratio': pca_2d.explained_variance_ratio_.tolist(),
    'pca_coordinates': X_pca.tolist()
}

with open('data/processed/3_03_visualization_data_first_purchase.json', 'w', encoding='utf-8') as f:
    json.dump(viz_data, f, ensure_ascii=False, indent=2)
print(f"✅ Données de visualisation sauvegardées")

# 5. Affichage du résumé
print(f"\n📊 RÉSUMÉ DE LA SEGMENTATION :")
print(f"   - {optimal_k} clusters")
print(f"   - {len(labels):,} clients segmentés")
print(f"   - Score silhouette : {silhouette_score(X_scaled_adapted, labels):.3f}")

print(f"\n✅ Notebook 3 terminé avec succès !")