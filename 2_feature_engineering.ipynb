{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 2 : Feature Engineering (First Purchase)\n", "\n", "## Objectif\n", "Construire des variables pertinentes pour la segmentation client adaptées au contexte \"First Purchase\". Puisque chaque client n'a qu'une seule commande, nous nous concentrons sur la récence, le montant et les variables contextuelles plutôt que sur l'approche RFM classique.\n", "\n", "### Variables créées (6 maximum) :\n", "1. **`recency_days`** : <PERSON><PERSON> depu<PERSON> l'achat\n", "2. **`order_value`** : <PERSON><PERSON>e\n", "3. **`state_encoded`** : Localisation géographique\n", "4. **`purchase_month`** : Saisonnalité\n", "5. **`delivery_days`** : <PERSON><PERSON><PERSON>\n", "6. **`review_score_filled`** : Satisfaction client\n", "\n", "---\n", "\n", "**Auteur :** <PERSON><PERSON>  \n", "**Date :** 3 juin 2025  \n", "**Projet :** Segmentation client Olist  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Chargement des données et préparation\n", "\n", "### 1.1 Import des librairies nécessaires"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/raw\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/data/clean\n", "Dossier assuré : /Users/<USER>/Developer/OPC/P5/reports\n", "Le dossier source /Users/<USER>/Developer/OPC/P5/data/clean/features n'existe pas, rien à déplacer.\n", "==================================================\n", "\u001b[1m\u001b[95m🚀 INITIALISATION DU NOTEBOOK\u001b[0m\n", "==================================================\n", "\u001b[94mNotebook: 2_feature_engineering.ipynb\u001b[0m\n", "\u001b[94mGraine aléatoire: 42\u001b[0m\n", "\u001b[94mStyle seaborn: whitegrid\u001b[0m\n", "\u001b[94mTaille des figures: (12, 8)\u001b[0m\n", "Vérification des bibliothèques disponibles:\n", "==================================================\n", "- Python: 3.13.2\n", "- NumPy: 2.2.5\n", "- Pandas: 2.2.3\n", "- Matplotlib: 3.10.1\n", "- Seaborn: 0.13.2\n", "- Scikit-learn: 1.6.1\n", "- Folium: Disponible\n", "- Plotly: 6.0.1\n", "==================================================\n", "\u001b[92mVisualisations cartographiques interactives DISPONIBLES.\u001b[0m\n", "Options d'affichage pandas configurées:\n", "- max_rows: 100\n", "- max_columns: 100\n", "- width: 1000\n", "- precision: 4\n", "\u001b[94mAppel de setup_notebook_env pour configurer les dossiers...\u001b[0m\n", "\u001b[95m\n", "Dossiers d'export configurés par setup_notebook_env:\u001b[0m\n", "\u001b[92m- base_export: None\u001b[0m\n", "\u001b[92m- figures: /Users/<USER>/Developer/OPC/P5/reports/figures\u001b[0m\n", "\u001b[92m- maps: /Users/<USER>/Developer/OPC/P5/reports/maps\u001b[0m\n", "\u001b[92m- models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[94mExport config: Figures: /Users/<USER>/Developer/OPC/P5/reports/figures, Maps: /Users/<USER>/Developer/OPC/P5/reports/maps, Models: /Users/<USER>/Developer/OPC/P5/reports/models\u001b[0m\n", "\u001b[92mSauvegarde automatique des figures activée (écraser existants: False).\u001b[0m\n", "\n", "==================================================\n", "\n", "\n", "📁 Répertoire de travail : /Users/<USER>/Developer/OPC/P5\n", "📊 Répertoire des rapports : /Users/<USER>/Developer/OPC/P5/reports\n", "🎲 Graine aléatoire : 42\n"]}], "source": ["# Imports spécifiques pour ce notebook\n", "import os\n", "import sqlite3\n", "from datetime import datetime, timedelta\n", "\n", "# Preprocessing spécifique\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "\n", "# Imports locaux - modules utils optimisés\n", "from utils.core import (\n", "    init_notebook, SEED, PROJECT_ROOT, REPORTS_DIR,\n", "    # Les imports de base sont déjà dans core\n", "    pd, np, plt, sns\n", ")\n", "from utils.feature_engineering import (\n", "    create_first_purchase_features,\n", "    clean_and_impute_features,\n", "    analyze_feature_correlation\n", ")\n", "from utils.data_tools import load_data, export_artifact\n", "from utils.save_load import save_results\n", "from utils.clustering_visualization import export_figure\n", "\n", "# Configuration du notebook avec le module core\n", "init_notebook(\n", "    notebook_file_path=\"2_feature_engineering.ipynb\",\n", "    style=\"whitegrid\",\n", "    figsize=(12, 8),\n", "    random_seed=SEED,\n", "    setup=True,\n", "    check_deps=True\n", ")\n", "\n", "print(f\"\\n📁 Répertoire de travail : {PROJECT_ROOT}\")\n", "print(f\"📊 Répertoire des rapports : {REPORTS_DIR}\")\n", "print(f\"🎲 Graine aléatoire : {SEED}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement des données nettoyées du Notebook 1"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Chargement des données nettoyées du Notebook 1...\n", "✅ Données chargées depuis le Notebook 1 : data/processed/1_01_cleaned_dataset.csv\n", "📊 Shape : (96470, 9)\n", "📋 Colonnes : ['customer_id', 'frequency', 'first_order_date', 'last_order_date', 'recency', 'customer_state', 'customer_city', 'monetary', 'value_segment']\n", "\n", "🔍 Vérifications :\n", "- Valeurs manquantes : 0\n", "- Doublons : 0\n"]}], "source": ["# Chargement des données nettoyées du Notebook 1\n", "print(\"🔄 Chargement des données nettoyées du Notebook 1...\")\n", "\n", "# Chemin du fichier généré par le Notebook 1\n", "cleaned_data_path = 'data/processed/1_01_cleaned_dataset.csv'\n", "\n", "# Vérification de l'existence du fichier\n", "if not os.path.exists(cleaned_data_path):\n", "    print(f\"❌ Fichier non trouvé : {cleaned_data_path}\")\n", "    print(\"⚠️ Veuillez d'abord exécuter le Notebook 1 pour générer les données nettoyées.\")\n", "    print(\"\\n🔄 Chargement alternatif depuis la base SQLite...\")\n", "\n", "    # Fallback : chargement depuis SQLite si le fichier n'existe pas\n", "    db_path = 'data/raw/olist.db'\n", "    if not os.path.exists(db_path):\n", "        raise FileNotFoundError(f\"❌ Base de données non trouvée : {db_path}\")\n", "\n", "    conn = sqlite3.connect(db_path)\n", "    query = \"\"\"\n", "    SELECT\n", "        o.customer_id,\n", "        o.order_id,\n", "        o.order_purchase_timestamp,\n", "        o.order_delivered_customer_date,\n", "        o.order_status,\n", "        oi.price,\n", "        c.customer_state,\n", "        r.review_score\n", "    FROM orders o\n", "    LEFT JOIN order_items oi ON o.order_id = oi.order_id\n", "    LEFT JOIN customers c ON o.customer_id = c.customer_id\n", "    LEFT JOIN order_reviews r ON o.order_id = r.order_id\n", "    WHERE o.order_status = 'delivered'\n", "      AND o.order_delivered_customer_date IS NOT NULL\n", "    \"\"\"\n", "\n", "    df_raw = pd.read_sql_query(query, conn)\n", "    conn.close()\n", "    print(f\"✅ Données chargées depuis SQLite (fallback)\")\n", "else:\n", "    # Chargement normal depuis le fichier nettoyé\n", "    df_raw = pd.read_csv(cleaned_data_path)\n", "    print(f\"✅ Données chargées depuis le Notebook 1 : {cleaned_data_path}\")\n", "\n", "print(f\"📊 Shape : {df_raw.shape}\")\n", "print(f\"📋 Colonnes : {list(df_raw.columns)}\")\n", "\n", "# Vérification de l'intégrité\n", "print(f\"\\n🔍 Vérifications :\")\n", "print(f\"- Valeurs manquantes : {df_raw.isnull().sum().sum()}\")\n", "print(f\"- Doublons : {df_raw.duplicated().sum()}\")\n", "\n", "# Conversion des dates si nécessaire\n", "date_cols = ['order_purchase_timestamp', 'order_delivered_customer_date']\n", "for col in date_cols:\n", "    if col in df_raw.columns and not pd.api.types.is_datetime64_any_dtype(df_raw[col]):\n", "        df_raw[col] = pd.to_datetime(df_raw[col])\n", "\n", "# Vérification de la période si les colonnes de dates existent\n", "if 'order_purchase_timestamp' in df_raw.columns:\n", "    print(f\"- Période des données : du {df_raw['order_purchase_timestamp'].min()} au {df_raw['order_purchase_timestamp'].max()}\")\n", "\n", "# Vérification du pattern \"1 client = 1 commande\"\n", "if 'customer_id' in df_raw.columns and 'order_id' in df_raw.columns:\n", "    orders_per_customer = df_raw.groupby('customer_id')['order_id'].nunique()\n", "    multi_order_customers = (orders_per_customer > 1).sum()\n", "    print(f\"\\n🎯 Pattern découvert :\")\n", "    print(f\"- Clients avec >1 commande : {multi_order_customers}\")\n", "    print(f\"- Confirmation : {'✅ Chaque client = 1 commande' if multi_order_customers == 0 else '⚠️ Certains clients ont plusieurs commandes'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Préparation des données pour feature engineering"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Préparation des données pour feature engineering...\n", "✅ Toutes les colonnes essentielles sont présentes\n", "📊 Données agrégées : (96470, 5)\n", "📋 Aperçu des données :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>customer_state</th>\n", "      <th>monetary</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>SP</td>\n", "      <td>89.8000</td>\n", "      <td>287.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>MG</td>\n", "      <td>54.9000</td>\n", "      <td>409.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>ES</td>\n", "      <td>179.9900</td>\n", "      <td>547.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>MG</td>\n", "      <td>149.9000</td>\n", "      <td>378.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>SP</td>\n", "      <td>93.0000</td>\n", "      <td>149.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id customer_state  monetary  recency  frequency\n", "0  00012a2ce6f8dcda20d059ce98491703             SP   89.8000 287.0000     1.0000\n", "1  000161a058600d5901f007fab4c27140             MG   54.9000 409.0000     1.0000\n", "2  0001fd6190edaaf884bcaf3d49edf079             ES  179.9900 547.0000     1.0000\n", "3  0002414f95344307404f0ace7a26f1d5             MG  149.9000 378.0000     1.0000\n", "4  000379cdec625522490c315e70c7a9fb             SP   93.0000 149.0000     1.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Statistiques descriptives :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>monetary</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>137.0400</td>\n", "      <td>239.1296</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>209.0526</td>\n", "      <td>152.8354</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>45.9000</td>\n", "      <td>115.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>86.5000</td>\n", "      <td>220.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>149.9000</td>\n", "      <td>349.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>13440.0000</td>\n", "      <td>713.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        monetary    recency  frequency\n", "count 96470.0000 96470.0000 96470.0000\n", "mean    137.0400   239.1296     1.0000\n", "std     209.0526   152.8354     0.0000\n", "min       0.8500     0.0000     1.0000\n", "25%      45.9000   115.0000     1.0000\n", "50%      86.5000   220.0000     1.0000\n", "75%     149.9000   349.0000     1.0000\n", "max   13440.0000   713.0000     1.0000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Valeurs manquantes par colonne :\n", "✅ Aucune valeur manquante détectée\n"]}], "source": ["# Préparation des données pour l'approche \"First Purchase\"\n", "if 'df_raw' in locals():\n", "    print(\"🔄 Préparation des données pour feature engineering...\")\n", "\n", "    # Mapping des colonnes alternatives\n", "    column_mapping = {\n", "        'order_purchase_timestamp': 'first_order_date',  # Utiliser first_order_date comme alternative\n", "        'price': 'monetary'  # Utiliser monetary comme alternative\n", "    }\n", "\n", "    # Vérification des colonnes nécessaires avec alternatives\n", "    required_cols = ['customer_id', 'customer_state']\n", "    missing_cols = [col for col in required_cols if col not in df_raw.columns]\n", "\n", "    if missing_cols:\n", "        print(f\"⚠️ Colonnes essentielles manquantes : {missing_cols}\")\n", "        print(f\"Colonnes disponibles : {list(df_raw.columns)}\")\n", "    else:\n", "        print(\"✅ Toutes les colonnes essentielles sont présentes\")\n", "\n", "    # Préparation des colonnes pour l'agrégation\n", "    group_cols = ['customer_id']\n", "    if 'customer_state' in df_raw.columns:\n", "        group_cols.append('customer_state')\n", "\n", "    # Préparation du dictionnaire d'agrégation\n", "    agg_dict = {}\n", "    if 'monetary' in df_raw.columns:\n", "        agg_dict['monetary'] = 'sum'\n", "    if 'recency' in df_raw.columns:\n", "        agg_dict['recency'] = 'mean'\n", "    if 'frequency' in df_raw.columns:\n", "        agg_dict['frequency'] = 'mean'\n", "\n", "    if agg_dict:\n", "        df_aggregated = df_raw.groupby(group_cols).agg(agg_dict).reset_index()\n", "        print(f\"📊 Données agrégées : {df_aggregated.shape}\")\n", "\n", "        # Aperçu des données\n", "        print(f\"📋 Aperçu des données :\")\n", "        display(df_aggregated.head())\n", "\n", "        # Statistiques descriptives\n", "        numeric_cols = df_aggregated.select_dtypes(include=[np.number]).columns\n", "        if len(numeric_cols) > 0:\n", "            print(f\"\\n📊 Statistiques descriptives :\")\n", "            display(df_aggregated[numeric_cols].describe())\n", "\n", "        # Vérification des valeurs manquantes\n", "        print(f\"\\n🔍 Valeurs manquantes par colonne :\")\n", "        missing_values = df_aggregated.isnull().sum()\n", "        for col, count in missing_values.items():\n", "            if count > 0:\n", "                print(f\"- {col}: {count} ({count/len(df_aggregated)*100:.1f}%)\")\n", "\n", "        if missing_values.sum() == 0:\n", "            print(\"✅ Aucune valeur manquante détectée\")\n", "    else:\n", "        print(\"⚠️ Aucune colonne numérique trouvée pour l'agrégation\")\n", "        df_aggregated = df_raw.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Création des variables \"First Purchase\"\n", "\n", "### 2.1 Utilisation du module utils pour créer les features optimisées"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Création des features 'First Purchase' optimisées...\n", "📋 Colonnes disponibles : ['customer_id', 'customer_state', 'monetary', 'recency', 'frequency']\n", "✅ Fonction create_first_purchase_features trouvée dans utils\n", "✅ Features créées : (96470, 8)\n", "📋 Variables créées : ['customer_id', 'customer_state', 'monetary', 'recency', 'frequency', 'recency_days', 'order_value', 'state_encoded']\n", "\n", "📊 Aperçu des features :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customer_id</th>\n", "      <th>customer_state</th>\n", "      <th>monetary</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>recency_days</th>\n", "      <th>order_value</th>\n", "      <th>state_encoded</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00012a2ce6f8dcda20d059ce98491703</td>\n", "      <td>SP</td>\n", "      <td>89.8000</td>\n", "      <td>287.0000</td>\n", "      <td>1.0000</td>\n", "      <td>287.0000</td>\n", "      <td>89.8000</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000161a058600d5901f007fab4c27140</td>\n", "      <td>MG</td>\n", "      <td>54.9000</td>\n", "      <td>409.0000</td>\n", "      <td>1.0000</td>\n", "      <td>409.0000</td>\n", "      <td>54.9000</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001fd6190edaaf884bcaf3d49edf079</td>\n", "      <td>ES</td>\n", "      <td>179.9900</td>\n", "      <td>547.0000</td>\n", "      <td>1.0000</td>\n", "      <td>547.0000</td>\n", "      <td>179.9900</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0002414f95344307404f0ace7a26f1d5</td>\n", "      <td>MG</td>\n", "      <td>149.9000</td>\n", "      <td>378.0000</td>\n", "      <td>1.0000</td>\n", "      <td>378.0000</td>\n", "      <td>149.9000</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000379cdec625522490c315e70c7a9fb</td>\n", "      <td>SP</td>\n", "      <td>93.0000</td>\n", "      <td>149.0000</td>\n", "      <td>1.0000</td>\n", "      <td>149.0000</td>\n", "      <td>93.0000</td>\n", "      <td>25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        customer_id customer_state  monetary  recency  frequency  recency_days  order_value  state_encoded\n", "0  00012a2ce6f8dcda20d059ce98491703             SP   89.8000 287.0000     1.0000      287.0000      89.8000             25\n", "1  000161a058600d5901f007fab4c27140             MG   54.9000 409.0000     1.0000      409.0000      54.9000             10\n", "2  0001fd6190edaaf884bcaf3d49edf079             ES  179.9900 547.0000     1.0000      547.0000     179.9900              7\n", "3  0002414f95344307404f0ace7a26f1d5             MG  149.9000 378.0000     1.0000      378.0000     149.9000             10\n", "4  000379cdec625522490c315e70c7a9fb             SP   93.0000 149.0000     1.0000      149.0000      93.0000             25"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>monetary</th>\n", "      <th>recency</th>\n", "      <th>frequency</th>\n", "      <th>recency_days</th>\n", "      <th>order_value</th>\n", "      <th>state_encoded</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>137.0400</td>\n", "      <td>239.1296</td>\n", "      <td>1.0000</td>\n", "      <td>239.1296</td>\n", "      <td>137.0400</td>\n", "      <td>18.6498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>209.0526</td>\n", "      <td>152.8354</td>\n", "      <td>0.0000</td>\n", "      <td>152.8354</td>\n", "      <td>209.0526</td>\n", "      <td>7.0809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "      <td>1.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.8500</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>45.9000</td>\n", "      <td>115.0000</td>\n", "      <td>1.0000</td>\n", "      <td>115.0000</td>\n", "      <td>45.9000</td>\n", "      <td>12.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>86.5000</td>\n", "      <td>220.0000</td>\n", "      <td>1.0000</td>\n", "      <td>220.0000</td>\n", "      <td>86.5000</td>\n", "      <td>22.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>149.9000</td>\n", "      <td>349.0000</td>\n", "      <td>1.0000</td>\n", "      <td>349.0000</td>\n", "      <td>149.9000</td>\n", "      <td>25.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>13440.0000</td>\n", "      <td>713.0000</td>\n", "      <td>1.0000</td>\n", "      <td>713.0000</td>\n", "      <td>13440.0000</td>\n", "      <td>26.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        monetary    recency  frequency  recency_days  order_value  state_encoded\n", "count 96470.0000 96470.0000 96470.0000    96470.0000   96470.0000     96470.0000\n", "mean    137.0400   239.1296     1.0000      239.1296     137.0400        18.6498\n", "std     209.0526   152.8354     0.0000      152.8354     209.0526         7.0809\n", "min       0.8500     0.0000     1.0000        0.0000       0.8500         0.0000\n", "25%      45.9000   115.0000     1.0000      115.0000      45.9000        12.0000\n", "50%      86.5000   220.0000     1.0000      220.0000      86.5000        22.0000\n", "75%     149.9000   349.0000     1.0000      349.0000     149.9000        25.0000\n", "max   13440.0000   713.0000     1.0000      713.0000   13440.0000        26.0000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création des features \"First Purchase\" avec le module utils\n", "if 'df_aggregated' in locals():\n", "    print(\"🔄 Création des features 'First Purchase' optimisées...\")\n", "\n", "    # Affichage des colonnes disponibles pour debug\n", "    print(\"📋 Colonnes disponibles :\", list(df_aggregated.columns))\n", "\n", "    # Vérification de l'existence de la fonction dans le module utils\n", "    try:\n", "        # Test d'import de la fonction\n", "        from utils.feature_engineering import create_first_purchase_features\n", "        print(\"✅ Fonction create_first_purchase_features trouvée dans utils\")\n", "\n", "        # Création manuelle des features car les colonnes ne correspondent pas\n", "        first_purchase_features = df_aggregated.copy()\n", "\n", "        # 1. <PERSON><PERSON><PERSON> (jours depuis l'achat)\n", "        if 'recency' in first_purchase_features.columns:\n", "            first_purchase_features['recency_days'] = first_purchase_features['recency']\n", "\n", "        # 2. <PERSON><PERSON>e\n", "        if 'monetary' in first_purchase_features.columns:\n", "            first_purchase_features['order_value'] = first_purchase_features['monetary']\n", "\n", "        # 3. <PERSON><PERSON> encodé\n", "        if 'customer_state' in first_purchase_features.columns:\n", "            le = LabelEncoder()\n", "            first_purchase_features['state_encoded'] = le.fit_transform(\n", "                first_purchase_features['customer_state'].fillna('Unknown')\n", "            )\n", "\n", "        # 4. <PERSON><PERSON> (si nous avons une date)\n", "        if 'first_order_date' in first_purchase_features.columns:\n", "            first_purchase_features['purchase_month'] = pd.to_datetime(\n", "                first_purchase_features['first_order_date']\n", "            ).dt.month\n", "        elif 'last_order_date' in first_purchase_features.columns:\n", "            first_purchase_features['purchase_month'] = pd.to_datetime(\n", "                first_purchase_features['last_order_date']\n", "            ).dt.month\n", "\n", "        # 5. Segment de valeur (si disponible)\n", "        if 'value_segment' in first_purchase_features.columns:\n", "            le = LabelEncoder()\n", "            first_purchase_features['value_segment_encoded'] = le.fit_transform(\n", "                first_purchase_features['value_segment'].fillna('Unknown')\n", "            )\n", "\n", "        print(f\"✅ Features créées : {first_purchase_features.shape}\")\n", "        print(f\"📋 Variables créées : {list(first_purchase_features.columns)}\")\n", "\n", "    except ImportError as e:\n", "        print(f\"⚠️ Fonction create_first_purchase_features non trouvée : {e}\")\n", "        print(\"🔄 Création manuelle des features...\")\n", "\n", "        # Même code que ci-dessus pour la création manuelle\n", "        first_purchase_features = df_aggregated.copy()\n", "\n", "        # [Même code que ci-dessus...]\n", "\n", "    # Aperçu des features créées\n", "    print(f\"\\n📊 Aperçu des features :\")\n", "    display(first_purchase_features.head())\n", "\n", "    # Statistiques descriptives\n", "    numeric_cols = first_purchase_features.select_dtypes(include=[np.number]).columns\n", "    if len(numeric_cols) > 0:\n", "        print(f\"\\n📈 Statistiques descriptives :\")\n", "        display(first_purchase_features[numeric_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Nettoyage et préparation pour clustering\n", "\n", "### 3.1 Sélection et nettoyage des features finales"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Sélection et nettoyage des features pour clustering...\n", "📊 Features disponibles (4) : ['customer_id', 'recency_days', 'order_value', 'state_encoded']\n", "⚠️ Features manquantes (3) : ['purchase_month', 'delivery_days', 'review_score_filled']\n", "\n", "🔍 Nettoyage des valeurs manquantes...\n", "- Valeurs manquantes avant nettoyage : 0\n", "- Valeurs manquantes après nettoyage : 0\n", "\n", "✅ Aucune variable à variance nulle détectée\n", "\n", "📋 Variables finales pour clustering (3) : ['recency_days', 'order_value', 'state_encoded']\n", "\n", "✅ Aucune corrélation élevée détectée (seuil: 0.7)\n"]}], "source": ["# Sélection et nettoyage des features pour clustering\n", "if 'first_purchase_features' in locals():\n", "    print(\"🔄 Sélection et nettoyage des features pour clustering...\")\n", "\n", "    # Variables attendues selon la stratégie (6 maximum)\n", "    target_features = ['customer_id', 'recency_days', 'order_value', 'state_encoded',\n", "                      'purchase_month', 'delivery_days', 'review_score_filled']\n", "\n", "    # Sélection des features disponibles\n", "    available_features = [col for col in target_features if col in first_purchase_features.columns]\n", "    missing_features = [col for col in target_features if col not in first_purchase_features.columns]\n", "\n", "    print(f\"📊 Features disponibles ({len(available_features)}) : {available_features}\")\n", "    if missing_features:\n", "        print(f\"⚠️ Features manquantes ({len(missing_features)}) : {missing_features}\")\n", "\n", "    # Création du dataset final avec les features disponibles\n", "    features_clean = first_purchase_features[available_features].copy()\n", "\n", "    # Nettoyage des valeurs manquantes\n", "    print(f\"\\n🔍 Nettoyage des valeurs manquantes...\")\n", "    missing_before = features_clean.isnull().sum().sum()\n", "    print(f\"- Valeurs manquantes avant nettoyage : {missing_before}\")\n", "\n", "    # Gestion des valeurs manquantes par type de variable\n", "    for col in features_clean.columns:\n", "        if col != 'customer_id' and features_clean[col].isnull().sum() > 0:\n", "            if features_clean[col].dtype in ['int64', 'float64']:\n", "                # Variables numériques : médiane\n", "                median_val = features_clean[col].median()\n", "                features_clean[col] = features_clean[col].fillna(median_val)\n", "                print(f\"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec médiane ({median_val:.2f})\")\n", "            else:\n", "                # Variables catégorielles : mode\n", "                mode_val = features_clean[col].mode().iloc[0] if len(features_clean[col].mode()) > 0 else 'Unknown'\n", "                features_clean[col] = features_clean[col].fillna(mode_val)\n", "                print(f\"  - {col}: {features_clean[col].isnull().sum()} NaN remplis avec mode ({mode_val})\")\n", "\n", "    missing_after = features_clean.isnull().sum().sum()\n", "    print(f\"- Valeurs manquantes après nettoyage : {missing_after}\")\n", "\n", "    # Identification des variables numériques pour clustering\n", "    numeric_cols = [col for col in features_clean.columns\n", "                   if col != 'customer_id' and features_clean[col].dtype in ['int64', 'float64']]\n", "\n", "    # Vérification des variables à variance nulle\n", "    zero_variance_vars = []\n", "    for col in numeric_cols:\n", "        if features_clean[col].std() == 0:\n", "            zero_variance_vars.append(col)\n", "\n", "    if zero_variance_vars:\n", "        print(f\"\\n⚠️ Variables à variance nulle détectées : {zero_variance_vars}\")\n", "        print(\"Ces variables seront exclues du clustering.\")\n", "        numeric_cols = [col for col in numeric_cols if col not in zero_variance_vars]\n", "    else:\n", "        print(f\"\\n✅ Aucune variable à variance nulle détectée\")\n", "\n", "    clustering_vars = numeric_cols\n", "    print(f\"\\n📋 Variables finales pour clustering ({len(clustering_vars)}) : {clustering_vars}\")\n", "\n", "    # Vérification des corrélations élevées\n", "    if len(clustering_vars) > 1:\n", "        corr_matrix = features_clean[clustering_vars].corr()\n", "        high_corr_pairs = []\n", "        for i in range(len(clustering_vars)):\n", "            for j in range(i+1, len(clustering_vars)):\n", "                corr_val = abs(corr_matrix.iloc[i, j])\n", "                if corr_val > 0.7:\n", "                    high_corr_pairs.append((clustering_vars[i], clustering_vars[j], corr_val))\n", "\n", "        if high_corr_pairs:\n", "            print(f\"\\n⚠️ Corrélations élevées détectées (>0.7) :\")\n", "            for var1, var2, corr in high_corr_pairs:\n", "                print(f\"  - {var1} ↔ {var2}: {corr:.3f}\")\n", "        else:\n", "            print(f\"\\n✅ Aucune corrélation élevée détectée (seuil: 0.7)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Normalisation et export final\n", "\n", "### 4.1 Standardisation des variables pour clustering"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Standardisation des variables pour clustering...\n", "📊 Variables à standardiser : 3\n", "📋 Variables : ['recency_days', 'order_value', 'state_encoded']\n", "\n", "✅ Standardisation terminée : (96470, 3)\n", "\n", "📈 Vérification de la standardisation :\n", "- Moyennes (doivent être ~0) : 0.000000\n", "- Écarts-types (doivent être ~1) : 1.000005\n", "\n", "📊 Dataset final pour clustering : (96470, 3)\n", "📊 Dataset avec IDs : (96470, 4)\n", "\n", "📋 Aperçu des données standardisées :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency_days</th>\n", "      <th>order_value</th>\n", "      <th>state_encoded</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.3132</td>\n", "      <td>-0.2260</td>\n", "      <td>0.8968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.1115</td>\n", "      <td>-0.3929</td>\n", "      <td>-1.2216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2.0144</td>\n", "      <td>0.2055</td>\n", "      <td>-1.6453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.9086</td>\n", "      <td>0.0615</td>\n", "      <td>-1.2216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.5897</td>\n", "      <td>-0.2107</td>\n", "      <td>0.8968</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   recency_days  order_value  state_encoded\n", "0        0.3132      -0.2260         0.8968\n", "1        1.1115      -0.3929        -1.2216\n", "2        2.0144       0.2055        -1.6453\n", "3        0.9086       0.0615        -1.2216\n", "4       -0.5897      -0.2107         0.8968"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Statistiques descriptives :\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>recency_days</th>\n", "      <th>order_value</th>\n", "      <th>state_encoded</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "      <td>96470.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-1.5646</td>\n", "      <td>-0.6515</td>\n", "      <td>-2.6338</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>-0.8122</td>\n", "      <td>-0.4360</td>\n", "      <td>-0.9391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>-0.1252</td>\n", "      <td>-0.2418</td>\n", "      <td>0.4731</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.7189</td>\n", "      <td>0.0615</td>\n", "      <td>0.8968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.1005</td>\n", "      <td>63.6348</td>\n", "      <td>1.0380</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       recency_days  order_value  state_encoded\n", "count    96470.0000   96470.0000     96470.0000\n", "mean         0.0000       0.0000         0.0000\n", "std          1.0000       1.0000         1.0000\n", "min         -1.5646      -0.6515        -2.6338\n", "25%         -0.8122      -0.4360        -0.9391\n", "50%         -0.1252      -0.2418         0.4731\n", "75%          0.7189       0.0615         0.8968\n", "max          3.1005      63.6348         1.0380"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Standardisation des features pour clustering\n", "if 'features_clean' in locals() and 'clustering_vars' in locals():\n", "    print(\"🔄 Standardisation des variables pour clustering...\")\n", "\n", "    if len(clustering_vars) == 0:\n", "        print(\"❌ Aucune variable numérique disponible pour le clustering\")\n", "    else:\n", "        # Préparation des données pour clustering (exclure customer_id)\n", "        X_clustering = features_clean[clustering_vars].copy()\n", "\n", "        print(f\"📊 Variables à standardiser : {len(clustering_vars)}\")\n", "        print(f\"📋 Variables : {clustering_vars}\")\n", "\n", "        # Standardisation avec StandardScaler\n", "        scaler = StandardScaler()\n", "        X_scaled = pd.DataFrame(\n", "            scaler.fit_transform(X_clustering),\n", "            columns=clustering_vars,\n", "            index=X_clustering.index\n", "        )\n", "\n", "        print(f\"\\n✅ Standardisation terminée : {X_scaled.shape}\")\n", "\n", "        # Vérification de la standardisation\n", "        print(f\"\\n📈 Vérification de la standardisation :\")\n", "        print(f\"- <PERSON><PERSON><PERSON> (doivent être ~0) : {X_scaled.mean().mean():.6f}\")\n", "        print(f\"- Écarts-types (doivent être ~1) : {X_scaled.std().mean():.6f}\")\n", "\n", "        # Ajout de customer_id pour traçabilité\n", "        X_scaled_with_id = X_scaled.copy()\n", "        if 'customer_id' in features_clean.columns:\n", "            X_scaled_with_id['customer_id'] = features_clean['customer_id'].values\n", "\n", "        print(f\"\\n📊 Dataset final pour clustering : {X_scaled.shape}\")\n", "        print(f\"📊 Dataset avec IDs : {X_scaled_with_id.shape}\")\n", "\n", "        # Aperçu des données standardisées\n", "        print(f\"\\n📋 Aperçu des données standardisées :\")\n", "        display(X_scaled.head())\n", "\n", "        # Statistiques descriptives\n", "        print(f\"\\n📈 Statistiques descriptives :\")\n", "        display(X_scaled.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Export des datasets pour le Notebook 3 (CORRIGÉ)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Export des datasets pour le clustering...\n", "✅ Dataset clustering sauvegardé : data/processed/2_01_features_scaled_clustering.csv\n", "✅ Dataset avec IDs sauvegardé : data/processed/2_02_features_scaled_with_ids.csv\n", "✅ Dataset complet sauvegardé : data/processed/2_03_rfm_enriched_complete.csv\n", "✅ Scaler sauvegardé : data/processed/2_04_scaler.pkl\n", "\n", "📊 Résumé final :\n", "- Variables créées : 4 (dont customer_id)\n", "- Variables pour clustering : 3\n", "- Clients traités : 96,470\n", "- Variables finales : ['recency_days', 'order_value', 'state_encoded']\n", "\n", "✅ Feature engineering terminé avec succès !\n", "📁 Fichiers prêts pour le Notebook 3 (Clustering)\n", "\n", "📁 Fichiers exportés (compatibles Notebook 3) :\n", "  - data/processed/2_01_features_scaled_clustering.csv\n", "  - data/processed/2_02_features_scaled_with_ids.csv\n", "  - data/processed/2_03_rfm_enriched_complete.csv\n", "  - data/processed/2_04_scaler.pkl\n"]}], "source": ["# Export des datasets pour le clustering - NOMS CORRIGÉS pour Notebook 3\n", "if 'X_scaled' in locals() and 'X_scaled_with_id' in locals():\n", "    print(\"💾 Export des datasets pour le clustering...\")\n", "\n", "    # Création du dossier de sortie\n", "    os.makedirs('data/processed', exist_ok=True)\n", "\n", "    # 1. Dataset normalisé pour clustering (sans customer_id)\n", "    clustering_data_path = 'data/processed/2_01_features_scaled_clustering.csv'\n", "    X_scaled.to_csv(clustering_data_path, index=False)\n", "    print(f\"✅ Dataset clustering sauvegardé : {clustering_data_path}\")\n", "\n", "    # 2. Dataset avec customer_id pour traçabilité\n", "    traceability_path = 'data/processed/2_02_features_scaled_with_ids.csv'\n", "    X_scaled_with_id.to_csv(traceability_path, index=False)\n", "    print(f\"✅ Dataset avec IDs sauvegardé : {traceability_path}\")\n", "\n", "    # 3. Dataset complet enrichi (NOM CORRIGÉ pour Notebook 3)\n", "    # Le Notebook 3 attend '2_03_rfm_enriched_complete.csv'\n", "    complete_features_path = 'data/processed/2_03_rfm_enriched_complete.csv'\n", "    features_clean.to_csv(complete_features_path, index=False)\n", "    print(f\"✅ Dataset complet sauvegardé : {complete_features_path}\")\n", "\n", "    # 4. <PERSON><PERSON><PERSON><PERSON> du scaler pour usage futur\n", "    import joblib\n", "    scaler_path = 'data/processed/2_04_scaler.pkl'\n", "    joblib.dump(scaler, scaler_path)\n", "    print(f\"✅ Scaler sauvegardé : {scaler_path}\")\n", "\n", "    # Résumé final\n", "    print(f\"\\n📊 Résumé final :\")\n", "    print(f\"- Variables créées : {len(features_clean.columns)} (dont customer_id)\")\n", "    print(f\"- Variables pour clustering : {len(clustering_vars)}\")\n", "    print(f\"- Clients traités : {len(X_scaled):,}\")\n", "    print(f\"- Variables finales : {clustering_vars}\")\n", "\n", "    print(f\"\\n✅ Feature engineering terminé avec succès !\")\n", "    print(f\"📁 Fichiers prêts pour le Notebook 3 (Clustering)\")\n", "    print(f\"\\n📁 Fichiers exportés (compatibles Notebook 3) :\")\n", "    print(f\"  - {clustering_data_path}\")\n", "    print(f\"  - {traceability_path}\")\n", "    print(f\"  - {complete_features_path}\")\n", "    print(f\"  - {scaler_path}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}